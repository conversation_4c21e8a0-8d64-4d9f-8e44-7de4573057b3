<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyUI - macOS VSCode 自动化工具</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 页眉 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>PyUI</h1>
                    <span class="subtitle">macOS VSCode 自动化工具</span>
                </div>
                <div class="status-info">
                    <div class="status-item">
                        <i class="fas fa-server"></i>
                        <span id="server-status">连接中...</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-key"></i>
                        <input type="password" id="api-key" placeholder="API Key" value="default-api-key">
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
        <div class="container">
            <!-- 快速操作面板 -->
            <section class="quick-actions">
                <h2><i class="fas fa-bolt"></i> 快速操作</h2>
                <div class="action-grid">
                    <button class="action-btn" onclick="healthCheck(this)">
                        <i class="fas fa-heartbeat"></i>
                        健康检查
                    </button>
                    <button class="action-btn" onclick="getSystemInfo(this)">
                        <i class="fas fa-info-circle"></i>
                        系统信息
                    </button>
                    <button class="action-btn" onclick="takeScreenshot(this)">
                        <i class="fas fa-camera"></i>
                        截图
                    </button>
                    <button class="action-btn" onclick="launchVSCode(this)">
                        <i class="fas fa-play"></i>
                        启动 VSCode
                    </button>
                    <button class="action-btn" onclick="quitVSCode(this)">
                        <i class="fas fa-stop"></i>
                        关闭 VSCode
                    </button>
                    <button class="action-btn" onclick="focusVSCode(this)">
                        <i class="fas fa-code"></i>
                        聚焦 VSCode
                    </button>
                </div>
            </section>

            <!-- API 操作面板 -->
            <section class="api-panels">
                <!-- 鼠标操作 -->
                <div class="api-panel">
                    <h3><i class="fas fa-mouse-pointer"></i> 鼠标操作</h3>
                    <div class="form-group">
                        <label>坐标点击</label>
                        <div class="input-row">
                            <input type="number" id="click-x" placeholder="X 坐标" value="100">
                            <input type="number" id="click-y" placeholder="Y 坐标" value="100">
                            <select id="click-button">
                                <option value="left">左键</option>
                                <option value="right">右键</option>
                                <option value="middle">中键</option>
                            </select>
                            <button onclick="mouseClick()">点击</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>拖拽操作</label>
                        <div class="input-row">
                            <input type="number" id="drag-start-x" placeholder="起始 X" value="100">
                            <input type="number" id="drag-start-y" placeholder="起始 Y" value="100">
                            <input type="number" id="drag-end-x" placeholder="结束 X" value="200">
                            <input type="number" id="drag-end-y" placeholder="结束 Y" value="200">
                            <button onclick="mouseDrag()">拖拽</button>
                        </div>
                    </div>
                </div>

                <!-- 键盘操作 -->
                <div class="api-panel">
                    <h3><i class="fas fa-keyboard"></i> 键盘操作</h3>
                    <div class="form-group">
                        <label>文本输入</label>
                        <div class="input-row">
                            <input type="text" id="type-text" placeholder="要输入的文本" value="Hello PyUI!">
                            <input type="number" id="type-interval" placeholder="间隔(秒)" value="0.05" step="0.01">
                            <button onclick="typeText()">输入</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>组合键</label>
                        <div class="input-row">
                            <input type="text" id="hotkey-keys" placeholder="按键组合 (如: cmd,c)" value="cmd,c">
                            <button onclick="pressHotkey()">执行</button>
                        </div>
                    </div>
                </div>

                <!-- Git 操作 -->
                <div class="api-panel">
                    <h3><i class="fab fa-git-alt"></i> Git 操作</h3>
                    <div class="form-group">
                        <label>克隆仓库</label>
                        <div class="input-row">
                            <input type="url" id="git-url" placeholder="Git 仓库 URL" value="https://github.com/user/repo.git">
                            <input type="text" id="target-dir" placeholder="目标目录 (可选)">
                            <button onclick="cloneAndOpenProject()">克隆并打开</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>打开项目</label>
                        <div class="input-row">
                            <input type="text" id="project-path" placeholder="项目路径" value="~/Projects/my-project">
                            <button onclick="openProject()">打开项目</button>
                        </div>
                    </div>
                </div>

                <!-- VSCode 操作 -->
                <div class="api-panel">
                    <h3><i class="fas fa-code"></i> VSCode 操作</h3>
                    <div class="form-group">
                        <label>文件操作</label>
                        <div class="input-row">
                            <input type="text" id="file-path" placeholder="文件路径" value="src/main.py">
                            <button onclick="openFile()">打开文件</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>代码操作</label>
                        <div class="input-row">
                            <button onclick="runCurrentFile()">运行</button>
                            <button onclick="debugCurrentFile()">调试</button>
                            <button onclick="formatCode()">格式化</button>
                            <button onclick="saveAll()">保存全部</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>导航操作</label>
                        <div class="input-row">
                            <input type="text" id="search-text" placeholder="搜索文本">
                            <button onclick="findInFiles()">查找</button>
                            <input type="number" id="line-number" placeholder="行号" value="1">
                            <button onclick="gotoLine()">跳转</button>
                        </div>
                    </div>
                </div>

                <!-- Augment 插件 -->
                <div class="api-panel">
                    <h3><i class="fas fa-magic"></i> Augment 插件</h3>
                    <div class="form-group">
                        <label>AI 助手</label>
                        <div class="input-row">
                            <input type="text" id="augment-question" placeholder="输入问题或需求" value="帮我重构这个函数">
                            <button onclick="openAugmentPlugin()">打开 Augment</button>
                        </div>
                    </div>
                </div>

                <!-- HTTP 客户端 -->
                <div class="api-panel">
                    <h3><i class="fas fa-globe"></i> HTTP 客户端</h3>
                    <div class="form-group">
                        <label>GET 请求</label>
                        <div class="input-row">
                            <input type="url" id="get-url" placeholder="URL" value="https://httpbin.org/get">
                            <button onclick="httpGet()">发送 GET</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>POST 请求</label>
                        <div class="input-row">
                            <input type="url" id="post-url" placeholder="URL" value="https://httpbin.org/post">
                            <textarea id="post-data" placeholder="JSON 数据" rows="2">{"test": "data"}</textarea>
                            <button onclick="httpPost()">发送 POST</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 响应结果 -->
            <section class="response-section">
                <h2><i class="fas fa-terminal"></i> 响应结果</h2>
                <div class="response-container">
                    <div class="response-header">
                        <span id="response-status">等待请求...</span>
                        <button onclick="clearResponse()" class="clear-btn">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                    </div>
                    <pre id="response-content">等待 API 响应...</pre>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 PyUI - macOS VSCode 自动化工具 |
                <a href="https://github.com/your-repo" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
