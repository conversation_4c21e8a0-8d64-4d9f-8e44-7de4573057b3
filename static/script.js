// 全局配置
const API_BASE_URL = window.location.origin;
let currentApiKey = 'default-api-key';

// DOM 元素
const responseStatus = document.getElementById('response-status');
const responseContent = document.getElementById('response-content');
const apiKeyInput = document.getElementById('api-key');
const serverStatus = document.getElementById('server-status');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置 API Key 输入框事件
    apiKeyInput.addEventListener('input', function() {
        currentApiKey = this.value;
    });
    
    // 检查服务器状态
    checkServerStatus();
    
    // 定期检查服务器状态
    setInterval(checkServerStatus, 30000);
});

// 检查服务器状态
async function checkServerStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/health`);
        if (response.ok) {
            const data = await response.json();
            serverStatus.textContent = `服务器运行中 (v${data.version})`;
            serverStatus.style.color = '#28a745';
        } else {
            throw new Error('服务器响应异常');
        }
    } catch (error) {
        serverStatus.textContent = '服务器离线';
        serverStatus.style.color = '#dc3545';
    }
}

// 通用 API 请求函数
async function makeApiRequest(endpoint, method = 'GET', data = null, requireAuth = true, buttonElement = null) {
    const headers = {
        'Content-Type': 'application/json'
    };

    if (requireAuth) {
        headers['X-API-Key'] = currentApiKey;
    }

    const config = {
        method,
        headers
    };

    // 对于 POST 请求，如果没有数据则发送空对象
    if (method !== 'GET') {
        config.body = JSON.stringify(data || {});
    }

    // 保存按钮原始状态
    let originalButtonText = '';
    let originalButtonDisabled = false;

    if (buttonElement) {
        originalButtonText = buttonElement.innerHTML;
        originalButtonDisabled = buttonElement.disabled;
        buttonElement.disabled = true;
        buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        buttonElement.classList.add('loading');
    }

    try {
        updateResponseStatus('请求中...', 'info');
        showToast('正在处理请求...', 'info');

        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
        const responseData = await response.json();

        if (response.ok) {
            updateResponseStatus(`成功 (${response.status})`, 'success');
            updateResponseContent(responseData);
            showToast('请求成功完成！', 'success');
            return responseData;
        } else {
            updateResponseStatus(`错误 (${response.status})`, 'error');
            updateResponseContent(responseData);
            showToast(`请求失败: ${responseData.error || '未知错误'}`, 'error');
            throw new Error(responseData.error || '请求失败');
        }
    } catch (error) {
        updateResponseStatus('请求失败', 'error');
        updateResponseContent({ error: error.message });
        showToast(`网络错误: ${error.message}`, 'error');
        throw error;
    } finally {
        // 恢复按钮状态
        if (buttonElement) {
            buttonElement.disabled = originalButtonDisabled;
            buttonElement.innerHTML = originalButtonText;
            buttonElement.classList.remove('loading');
        }
    }
}

// 更新响应状态
function updateResponseStatus(status, type) {
    responseStatus.textContent = status;
    responseStatus.className = `status-${type}`;
}

// 更新响应内容
function updateResponseContent(data) {
    responseContent.textContent = JSON.stringify(data, null, 2);
}

// 清空响应
function clearResponse() {
    responseStatus.textContent = '等待请求...';
    responseStatus.className = '';
    responseContent.textContent = '等待 API 响应...';
}

// 显示 Toast 通知
function showToast(message, type = 'info', duration = 3000) {
    // 创建 toast 容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // 创建 toast 元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    // 根据类型设置图标
    let icon = 'fas fa-info-circle';
    if (type === 'success') icon = 'fas fa-check-circle';
    else if (type === 'error') icon = 'fas fa-exclamation-circle';
    else if (type === 'warning') icon = 'fas fa-exclamation-triangle';

    toast.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);

    // 自动移除
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

// ============ 快速操作函数 ============

// 健康检查
async function healthCheck(buttonElement) {
    try {
        const result = await makeApiRequest('/api/health', 'GET', null, false, buttonElement);
        showToast(`服务器健康状态: ${result.status}`, 'success');
    } catch (error) {
        console.error('健康检查失败:', error);
        showToast('健康检查失败，请检查服务器连接', 'error');
    }
}

// 获取系统信息
async function getSystemInfo(buttonElement) {
    try {
        const result = await makeApiRequest('/api/info', 'GET', null, true, buttonElement);
        const data = result.data;
        showToast(`屏幕尺寸: ${data.screen_size[0]}x${data.screen_size[1]}, VSCode运行: ${data.vscode_info.is_running ? '是' : '否'}`, 'success');
    } catch (error) {
        console.error('获取系统信息失败:', error);
        showToast('获取系统信息失败，请检查API密钥', 'error');
    }
}

// 截图
async function takeScreenshot(buttonElement) {
    try {
        const result = await makeApiRequest('/api/screen/screenshot', 'POST', {}, true, buttonElement);
        showToast(`截图已保存: ${result.screenshot_path}`, 'success');
    } catch (error) {
        console.error('截图失败:', error);
        showToast('截图失败，请检查权限设置', 'error');
    }
}

// 启动 VSCode
async function launchVSCode(buttonElement) {
    try {
        const result = await makeApiRequest('/api/vscode/launch', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('VSCode 启动成功', 'success');
        } else {
            showToast('VSCode 启动失败，请检查系统权限', 'error');
        }
    } catch (error) {
        console.error('启动 VSCode 失败:', error);
        showToast('启动 VSCode 失败，请检查系统配置', 'error');
    }
}

// 关闭 VSCode
async function quitVSCode(buttonElement) {
    try {
        // 显示确认对话框
        if (!confirm('确定要关闭 VSCode 吗？未保存的工作可能会丢失。')) {
            return;
        }

        const result = await makeApiRequest('/api/vscode/quit', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('VSCode 已成功关闭', 'success');
        } else {
            showToast('VSCode 关闭失败，请手动关闭', 'warning');
        }
    } catch (error) {
        console.error('关闭 VSCode 失败:', error);
        showToast('关闭 VSCode 失败，请手动关闭', 'error');
    }
}

// 聚焦 VSCode
async function focusVSCode(buttonElement) {
    try {
        const result = await makeApiRequest('/api/vscode/focus', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('VSCode 已成功聚焦', 'success');
        } else {
            showToast('VSCode 聚焦失败，请确保 VSCode 正在运行', 'warning');
        }
    } catch (error) {
        console.error('聚焦 VSCode 失败:', error);
        showToast('聚焦 VSCode 失败，请检查 VSCode 是否运行', 'error');
    }
}

// ============ 鼠标操作函数 ============

// 鼠标点击
async function mouseClick() {
    const x = parseInt(document.getElementById('click-x').value);
    const y = parseInt(document.getElementById('click-y').value);
    const button = document.getElementById('click-button').value;
    const buttonElement = event.target;

    if (isNaN(x) || isNaN(y)) {
        showToast('请输入有效的坐标', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/mouse/click', 'POST', { x, y, button }, true, buttonElement);
        if (result.success) {
            showToast(`${button}键点击 (${x}, ${y}) 成功`, 'success');
        }
    } catch (error) {
        console.error('鼠标点击失败:', error);
        showToast('鼠标点击失败，请检查坐标和权限', 'error');
    }
}

// 鼠标拖拽
async function mouseDrag() {
    const startX = parseInt(document.getElementById('drag-start-x').value);
    const startY = parseInt(document.getElementById('drag-start-y').value);
    const endX = parseInt(document.getElementById('drag-end-x').value);
    const endY = parseInt(document.getElementById('drag-end-y').value);
    const buttonElement = event.target;

    if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
        showToast('请输入有效的坐标', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/mouse/drag', 'POST', {
            start_x: startX,
            start_y: startY,
            end_x: endX,
            end_y: endY,
            duration: 1.0
        }, true, buttonElement);
        if (result.success) {
            showToast(`拖拽操作 (${startX},${startY}) → (${endX},${endY}) 成功`, 'success');
        }
    } catch (error) {
        console.error('鼠标拖拽失败:', error);
        showToast('鼠标拖拽失败，请检查坐标和权限', 'error');
    }
}

// ============ 键盘操作函数 ============

// 输入文本
async function typeText() {
    const text = document.getElementById('type-text').value;
    const interval = parseFloat(document.getElementById('type-interval').value);
    const buttonElement = event.target;

    if (!text) {
        showToast('请输入要输入的文本', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/keyboard/type', 'POST', { text, interval }, true, buttonElement);
        if (result.success) {
            showToast(`文本输入成功: "${text.length > 20 ? text.substring(0, 20) + '...' : text}"`, 'success');
        }
    } catch (error) {
        console.error('输入文本失败:', error);
        showToast('文本输入失败，请检查权限设置', 'error');
    }
}

// 按组合键
async function pressHotkey() {
    const keysStr = document.getElementById('hotkey-keys').value;
    const keys = keysStr.split(',').map(k => k.trim());
    const buttonElement = event.target;

    if (!keys.length || !keysStr) {
        showToast('请输入按键组合', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/keyboard/hotkey', 'POST', { keys }, true, buttonElement);
        if (result.success) {
            showToast(`组合键 "${keysStr}" 执行成功`, 'success');
        }
    } catch (error) {
        console.error('按组合键失败:', error);
        showToast('组合键执行失败，请检查按键格式和权限', 'error');
    }
}

// ============ Git 操作函数 ============

// 克隆并打开项目
async function cloneAndOpenProject() {
    const gitUrl = document.getElementById('git-url').value;
    const targetDir = document.getElementById('target-dir').value;
    const buttonElement = event.target;

    if (!gitUrl) {
        showToast('请输入 Git 仓库 URL', 'warning');
        return;
    }

    try {
        const data = { git_url: gitUrl };
        if (targetDir) {
            data.target_dir = targetDir;
        }

        const result = await makeApiRequest('/api/vscode/clone_and_open', 'POST', data, true, buttonElement);
        if (result.success) {
            showToast(`项目克隆并打开成功: ${gitUrl}`, 'success');
        }
    } catch (error) {
        console.error('克隆项目失败:', error);
        showToast('克隆项目失败，请检查 Git URL 和网络连接', 'error');
    }
}

// 打开项目
async function openProject() {
    const projectPath = document.getElementById('project-path').value;
    const buttonElement = event.target;

    if (!projectPath) {
        showToast('请输入项目路径', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/vscode/open_project', 'POST', { project_path: projectPath }, true, buttonElement);
        if (result.success) {
            showToast(`项目打开成功: ${projectPath}`, 'success');
        }
    } catch (error) {
        console.error('打开项目失败:', error);
        showToast('打开项目失败，请检查项目路径', 'error');
    }
}

// ============ VSCode 操作函数 ============

// 打开文件
async function openFile() {
    const filePath = document.getElementById('file-path').value;
    const buttonElement = event.target;

    if (!filePath) {
        showToast('请输入文件路径', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/vscode/open_file', 'POST', { file_path: filePath }, true, buttonElement);
        if (result.success) {
            showToast(`文件打开成功: ${filePath}`, 'success');
        }
    } catch (error) {
        console.error('打开文件失败:', error);
        showToast('打开文件失败，请检查文件路径和 VSCode 状态', 'error');
    }
}

// 运行当前文件
async function runCurrentFile() {
    const buttonElement = event.target;
    try {
        const result = await makeApiRequest('/api/vscode/run', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('文件运行成功', 'success');
        }
    } catch (error) {
        console.error('运行文件失败:', error);
        showToast('运行文件失败，请确保 VSCode 中有打开的文件', 'error');
    }
}

// 调试当前文件
async function debugCurrentFile() {
    const buttonElement = event.target;
    try {
        const result = await makeApiRequest('/api/vscode/debug', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('调试启动成功', 'success');
        }
    } catch (error) {
        console.error('调试文件失败:', error);
        showToast('调试启动失败，请确保 VSCode 中有打开的文件', 'error');
    }
}

// 格式化代码
async function formatCode() {
    const buttonElement = event.target;
    try {
        const result = await makeApiRequest('/api/vscode/format', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('代码格式化成功', 'success');
        }
    } catch (error) {
        console.error('格式化代码失败:', error);
        showToast('代码格式化失败，请确保 VSCode 中有打开的文件', 'error');
    }
}

// 保存全部
async function saveAll() {
    const buttonElement = event.target;
    try {
        const result = await makeApiRequest('/api/vscode/save_all', 'POST', {}, true, buttonElement);
        if (result.success) {
            showToast('所有文件保存成功', 'success');
        }
    } catch (error) {
        console.error('保存全部失败:', error);
        showToast('保存文件失败，请检查 VSCode 状态', 'error');
    }
}

// 在文件中查找
async function findInFiles() {
    const searchText = document.getElementById('search-text').value;
    const buttonElement = event.target;

    if (!searchText) {
        showToast('请输入搜索文本', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/vscode/find', 'POST', { search_text: searchText }, true, buttonElement);
        if (result.success) {
            showToast(`搜索 "${searchText}" 执行成功`, 'success');
        }
    } catch (error) {
        console.error('查找失败:', error);
        showToast('搜索失败，请确保 VSCode 正在运行', 'error');
    }
}

// 跳转到行
async function gotoLine() {
    const lineNumber = parseInt(document.getElementById('line-number').value);
    const buttonElement = event.target;

    if (isNaN(lineNumber) || lineNumber < 1) {
        showToast('请输入有效的行号', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/vscode/goto_line', 'POST', { line_number: lineNumber }, true, buttonElement);
        if (result.success) {
            showToast(`跳转到第 ${lineNumber} 行成功`, 'success');
        }
    } catch (error) {
        console.error('跳转到行失败:', error);
        showToast('跳转失败，请确保 VSCode 中有打开的文件', 'error');
    }
}

// 打开 Augment 插件
async function openAugmentPlugin() {
    const question = document.getElementById('augment-question').value;
    const buttonElement = event.target;

    try {
        const data = {};
        if (question) {
            data.question = question;
        }

        const result = await makeApiRequest('/api/vscode/augment', 'POST', data, true, buttonElement);
        if (result.success) {
            showToast('Augment 插件已打开', 'success');
        }
    } catch (error) {
        console.error('打开 Augment 插件失败:', error);
        showToast('打开 Augment 插件失败，请确保 VSCode 正在运行', 'error');
    }
}

// ============ HTTP 客户端函数 ============

// HTTP GET 请求
async function httpGet() {
    const url = document.getElementById('get-url').value;
    const buttonElement = event.target;

    if (!url) {
        showToast('请输入 URL', 'warning');
        return;
    }

    try {
        const result = await makeApiRequest('/api/http/get', 'POST', { url }, true, buttonElement);
        if (result.success) {
            showToast(`GET 请求成功: ${url}`, 'success');
        }
    } catch (error) {
        console.error('HTTP GET 请求失败:', error);
        showToast('HTTP GET 请求失败，请检查 URL 格式', 'error');
    }
}

// HTTP POST 请求
async function httpPost() {
    const url = document.getElementById('post-url').value;
    const dataStr = document.getElementById('post-data').value;
    const buttonElement = event.target;

    if (!url) {
        showToast('请输入 URL', 'warning');
        return;
    }

    let jsonData = null;
    if (dataStr) {
        try {
            jsonData = JSON.parse(dataStr);
        } catch (error) {
            showToast('JSON 数据格式错误', 'error');
            return;
        }
    }

    try {
        const result = await makeApiRequest('/api/http/post', 'POST', { url, json: jsonData }, true, buttonElement);
        if (result.success) {
            showToast(`POST 请求成功: ${url}`, 'success');
        }
    } catch (error) {
        console.error('HTTP POST 请求失败:', error);
        showToast('HTTP POST 请求失败，请检查 URL 和数据格式', 'error');
    }
}
