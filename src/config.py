"""
配置管理模块
"""
import os
from typing import Dict, Any

class Config:
    """应用配置类"""
    
    # HTTP 服务器配置
    HOST = os.getenv('PYUI_HOST', '127.0.0.1')
    PORT = int(os.getenv('PYUI_PORT', 8080))
    DEBUG = os.getenv('PYUI_DEBUG', 'False').lower() == 'true'
    
    # API 认证配置
    API_KEY = os.getenv('PYUI_API_KEY', 'default-api-key')
    
    # UI 自动化配置
    SCREENSHOT_DIR = os.getenv('PYUI_SCREENSHOT_DIR', './screenshots')
    CLICK_DELAY = float(os.getenv('PYUI_CLICK_DELAY', 0.1))
    TYPE_DELAY = float(os.getenv('PYUI_TYPE_DELAY', 0.05))
    
    # VSCode 配置
    VSCODE_APP_NAME = os.getenv('PYUI_VSCODE_APP_NAME', 'Visual Studio Code')
    VSCODE_BUNDLE_ID = os.getenv('PYUI_VSCODE_BUNDLE_ID', 'com.microsoft.VSCode')

    # Git 配置
    DEFAULT_PROJECTS_DIR = os.getenv('PYUI_PROJECTS_DIR', '~/Projects')
    
    # 日志配置
    LOG_LEVEL = os.getenv('PYUI_LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('PYUI_LOG_FILE', './pyui.log')
    
    @classmethod
    def get_all(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            key: getattr(cls, key) 
            for key in dir(cls) 
            if not key.startswith('_') and not callable(getattr(cls, key))
        }
    
    @classmethod
    def validate(cls) -> bool:
        """验证配置有效性"""
        try:
            # 创建截图目录
            os.makedirs(cls.SCREENSHOT_DIR, exist_ok=True)
            return True
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
