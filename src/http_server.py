"""
HTTP 服务器模块 - RESTful API 接口
"""
from flask import Flask, request, jsonify, send_from_directory, redirect, url_for
from flask_cors import CORS
from functools import wraps
from typing import Dict, Any, Callable
import traceback
import os
from src.ui_automation import UIAutomation
from src.vscode_integration import VSCodeIntegration
from src.http_client import HttpClient
from src.config import Config
from src.utils import logger

# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
static_folder = os.path.join(project_root, 'static')

app = Flask(__name__, static_folder=static_folder, static_url_path='/static')
CORS(app)

# 初始化组件
ui_automation = UIAutomation()
vscode_integration = VSCodeIntegration()
http_client = HttpClient()

def require_api_key(f: Callable) -> Callable:
    """API 密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if api_key != Config.API_KEY:
            return jsonify({'error': '无效的 API 密钥'}), 401
        return f(*args, **kwargs)
    return decorated_function

def handle_errors(f: Callable) -> Callable:
    """错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"API 错误: {e}")
            logger.error(traceback.format_exc())
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    return decorated_function

# ============ 静态文件和页面路由 ============

@app.route('/')
def index():
    """主页重定向到静态页面"""
    return redirect('/static/index.html')

# Flask 会自动处理静态文件，无需自定义路由

# ============ 基础 API ============

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0',
        'config': {
            'host': Config.HOST,
            'port': Config.PORT,
            'debug': Config.DEBUG
        }
    })

@app.route('/api/info', methods=['GET'])
@require_api_key
@handle_errors
def get_info():
    """获取系统信息"""
    return jsonify({
        'success': True,
        'data': {
            'screen_size': ui_automation.get_screen_size(),
            'mouse_position': ui_automation.get_mouse_position(),
            'vscode_info': vscode_integration.get_vscode_info()
        }
    })

# ============ 鼠标操作 API ============

@app.route('/api/mouse/click', methods=['POST'])
@require_api_key
@handle_errors
def mouse_click():
    """鼠标点击"""
    data = request.get_json()
    x = data.get('x')
    y = data.get('y')
    button = data.get('button', 'left')
    clicks = data.get('clicks', 1)
    
    if x is None or y is None:
        return jsonify({'error': '缺少坐标参数'}), 400
    
    success = ui_automation.click(x, y, button, clicks)
    return jsonify({'success': success})

@app.route('/api/mouse/double_click', methods=['POST'])
@require_api_key
@handle_errors
def mouse_double_click():
    """鼠标双击"""
    data = request.get_json()
    x = data.get('x')
    y = data.get('y')
    
    if x is None or y is None:
        return jsonify({'error': '缺少坐标参数'}), 400
    
    success = ui_automation.double_click(x, y)
    return jsonify({'success': success})

@app.route('/api/mouse/right_click', methods=['POST'])
@require_api_key
@handle_errors
def mouse_right_click():
    """鼠标右键点击"""
    data = request.get_json()
    x = data.get('x')
    y = data.get('y')
    
    if x is None or y is None:
        return jsonify({'error': '缺少坐标参数'}), 400
    
    success = ui_automation.right_click(x, y)
    return jsonify({'success': success})

@app.route('/api/mouse/drag', methods=['POST'])
@require_api_key
@handle_errors
def mouse_drag():
    """鼠标拖拽"""
    data = request.get_json()
    start_x = data.get('start_x')
    start_y = data.get('start_y')
    end_x = data.get('end_x')
    end_y = data.get('end_y')
    duration = data.get('duration', 1.0)
    
    if None in [start_x, start_y, end_x, end_y]:
        return jsonify({'error': '缺少坐标参数'}), 400
    
    success = ui_automation.drag(start_x, start_y, end_x, end_y, duration)
    return jsonify({'success': success})

@app.route('/api/mouse/scroll', methods=['POST'])
@require_api_key
@handle_errors
def mouse_scroll():
    """鼠标滚动"""
    data = request.get_json()
    x = data.get('x')
    y = data.get('y')
    clicks = data.get('clicks', 1)
    
    if x is None or y is None:
        return jsonify({'error': '缺少坐标参数'}), 400
    
    success = ui_automation.scroll(x, y, clicks)
    return jsonify({'success': success})

# ============ 键盘操作 API ============

@app.route('/api/keyboard/type', methods=['POST'])
@require_api_key
@handle_errors
def keyboard_type():
    """键盘输入文本"""
    data = request.get_json()
    text = data.get('text')
    interval = data.get('interval')
    
    if not text:
        return jsonify({'error': '缺少文本参数'}), 400
    
    success = ui_automation.type_text(text, interval)
    return jsonify({'success': success})

@app.route('/api/keyboard/press', methods=['POST'])
@require_api_key
@handle_errors
def keyboard_press():
    """键盘按键"""
    data = request.get_json()
    key = data.get('key')
    
    if not key:
        return jsonify({'error': '缺少按键参数'}), 400
    
    success = ui_automation.press_key(key)
    return jsonify({'success': success})

@app.route('/api/keyboard/hotkey', methods=['POST'])
@require_api_key
@handle_errors
def keyboard_hotkey():
    """键盘组合键"""
    data = request.get_json()
    keys = data.get('keys')

    if not keys or not isinstance(keys, list):
        return jsonify({'error': '缺少或无效的按键组合参数'}), 400

    success = ui_automation.press_hotkey(*keys)
    return jsonify({'success': success})

# ============ 屏幕操作 API ============

@app.route('/api/screen/screenshot', methods=['POST'])
@require_api_key
@handle_errors
def take_screenshot():
    """截取屏幕截图"""
    data = request.get_json() or {}
    save_path = data.get('save_path')

    screenshot_path = ui_automation.take_screenshot(save_path)
    return jsonify({
        'success': True,
        'screenshot_path': screenshot_path
    })

@app.route('/api/screen/find_image', methods=['POST'])
@require_api_key
@handle_errors
def find_image():
    """在屏幕上查找图像"""
    data = request.get_json()
    template_path = data.get('template_path')
    confidence = data.get('confidence', 0.8)

    if not template_path:
        return jsonify({'error': '缺少模板图像路径'}), 400

    position = ui_automation.find_image_on_screen(template_path, confidence)
    return jsonify({
        'success': position is not None,
        'position': position
    })

# ============ VSCode 操作 API ============

@app.route('/api/vscode/ensure_running', methods=['POST'])
@require_api_key
@handle_errors
def ensure_vscode_running():
    """确保 VSCode 正在运行"""
    success = vscode_integration.ensure_vscode_running()
    return jsonify({'success': success})

@app.route('/api/vscode/launch', methods=['POST'])
@require_api_key
@handle_errors
def launch_vscode():
    """启动 VSCode"""
    success = vscode_integration.launch_vscode()
    return jsonify({'success': success})

@app.route('/api/vscode/quit', methods=['POST'])
@require_api_key
@handle_errors
def quit_vscode():
    """关闭 VSCode"""
    success = vscode_integration.quit_vscode()
    return jsonify({'success': success})

@app.route('/api/vscode/focus', methods=['POST'])
@require_api_key
@handle_errors
def focus_vscode():
    """将焦点切换到 VSCode"""
    success = vscode_integration.focus_vscode()
    return jsonify({'success': success})

@app.route('/api/vscode/open_file', methods=['POST'])
@require_api_key
@handle_errors
def open_file():
    """打开文件"""
    data = request.get_json()
    file_path = data.get('file_path')

    if not file_path:
        return jsonify({'error': '缺少文件路径参数'}), 400

    success = vscode_integration.open_file(file_path)
    return jsonify({'success': success})

@app.route('/api/vscode/run', methods=['POST'])
@require_api_key
@handle_errors
def run_current_file():
    """运行当前文件"""
    success = vscode_integration.run_current_file()
    return jsonify({'success': success})

@app.route('/api/vscode/debug', methods=['POST'])
@require_api_key
@handle_errors
def debug_current_file():
    """调试当前文件"""
    success = vscode_integration.debug_current_file()
    return jsonify({'success': success})

@app.route('/api/vscode/find', methods=['POST'])
@require_api_key
@handle_errors
def find_in_files():
    """在文件中查找"""
    data = request.get_json()
    search_text = data.get('search_text')

    if not search_text:
        return jsonify({'error': '缺少搜索文本参数'}), 400

    success = vscode_integration.find_in_files(search_text)
    return jsonify({'success': success})

@app.route('/api/vscode/goto_line', methods=['POST'])
@require_api_key
@handle_errors
def goto_line():
    """跳转到指定行"""
    data = request.get_json()
    line_number = data.get('line_number')

    if line_number is None:
        return jsonify({'error': '缺少行号参数'}), 400

    success = vscode_integration.goto_line(line_number)
    return jsonify({'success': success})

@app.route('/api/vscode/format', methods=['POST'])
@require_api_key
@handle_errors
def format_code():
    """格式化代码"""
    success = vscode_integration.format_code()
    return jsonify({'success': success})

@app.route('/api/vscode/save_all', methods=['POST'])
@require_api_key
@handle_errors
def save_all():
    """保存所有文件"""
    success = vscode_integration.save_all()
    return jsonify({'success': success})

@app.route('/api/vscode/clone_and_open', methods=['POST'])
@require_api_key
@handle_errors
def clone_and_open_project():
    """克隆 Git 仓库并在 VSCode 中打开"""
    data = request.get_json()
    git_url = data.get('git_url')
    target_dir = data.get('target_dir')

    if not git_url:
        return jsonify({'error': '缺少 Git URL 参数'}), 400

    success = vscode_integration.clone_and_open_project(git_url, target_dir)
    return jsonify({'success': success})

@app.route('/api/vscode/open_project', methods=['POST'])
@require_api_key
@handle_errors
def open_project():
    """在 VSCode 中打开项目"""
    data = request.get_json()
    project_path = data.get('project_path')

    if not project_path:
        return jsonify({'error': '缺少项目路径参数'}), 400

    success = vscode_integration.open_project(project_path)
    return jsonify({'success': success})

@app.route('/api/vscode/augment', methods=['POST'])
@require_api_key
@handle_errors
def open_augment_plugin():
    """打开 Augment 插件并输入问题"""
    data = request.get_json()
    question = data.get('question')

    success = vscode_integration.open_augment_plugin(question)
    return jsonify({'success': success})

# ============ HTTP 客户端 API ============

@app.route('/api/http/get', methods=['POST'])
@require_api_key
@handle_errors
def http_get():
    """发送 GET 请求"""
    data = request.get_json()
    url = data.get('url')
    params = data.get('params')
    headers = data.get('headers')

    if not url:
        return jsonify({'error': '缺少 URL 参数'}), 400

    result = http_client.get(url, params=params, headers=headers)
    return jsonify({
        'success': True,
        'result': result
    })

@app.route('/api/http/post', methods=['POST'])
@require_api_key
@handle_errors
def http_post():
    """发送 POST 请求"""
    data = request.get_json()
    url = data.get('url')
    post_data = data.get('data')
    json_data = data.get('json')
    headers = data.get('headers')

    if not url:
        return jsonify({'error': '缺少 URL 参数'}), 400

    result = http_client.post(url, data=post_data, json_data=json_data, headers=headers)
    return jsonify({
        'success': True,
        'result': result
    })

def create_app():
    """创建 Flask 应用"""
    return app

def run_server():
    """运行服务器"""
    logger.info(f"启动 HTTP 服务器: http://{Config.HOST}:{Config.PORT}")
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG,
        threaded=True,
        use_reloader=False  # 禁用自动重载以避免线程问题
    )
