"""
VSCode 集成模块 - Visual Studio Code 特定功能封装
"""
import time
import subprocess
import os
import shutil
from typing import Optional, List, Dict, Any
from src.ui_automation import UIAutomation
from src.config import Config
from src.utils import logger, retry, log_execution, is_app_running, launch_app

class VSCodeIntegration:
    """Visual Studio Code 集成操作类"""
    
    def __init__(self):
        self.ui = UIAutomation()
        self.app_name = Config.VSCODE_APP_NAME
        self.bundle_id = Config.VSCODE_BUNDLE_ID
        
    @log_execution
    def ensure_vscode_running(self) -> bool:
        """确保 VSCode 正在运行"""
        if not is_app_running(self.app_name):
            logger.info("VSCode 未运行，正在启动...")
            return launch_app(self.app_name, self.bundle_id)
        return True

    @log_execution
    def launch_vscode(self) -> bool:
        """启动 VSCode"""
        try:
            # 检查是否已经运行
            if is_app_running(self.app_name):
                logger.info(f"VSCode 已在运行")
                return True

            logger.info("正在启动 VSCode...")

            # 尝试多种启动方式
            success = False

            # 方式1: 使用 Bundle ID 启动
            try:
                subprocess.run(['open', '-b', self.bundle_id], check=True, timeout=10)
                logger.info("使用 Bundle ID 启动 VSCode")
                success = True
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                logger.warning(f"Bundle ID 启动失败: {e}")

            # 方式2: 使用应用路径启动
            if not success:
                try:
                    app_path = f"/Applications/{self.app_name}.app"
                    subprocess.run(['open', app_path], check=True, timeout=10)
                    logger.info("使用应用路径启动 VSCode")
                    success = True
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                    logger.warning(f"应用路径启动失败: {e}")

            # 方式3: 尝试其他可能的应用名称
            if not success:
                possible_names = ["Visual Studio Code", "VSCode"]
                for name in possible_names:
                    try:
                        app_path = f"/Applications/{name}.app"
                        subprocess.run(['open', app_path], check=True, timeout=10)
                        logger.info(f"使用应用名称 '{name}' 启动 VSCode")
                        success = True
                        break
                    except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                        continue

            # 方式4: 尝试使用 code 命令
            if not success:
                try:
                    subprocess.run(['code'], check=True, timeout=10)
                    logger.info("使用 code 命令启动 VSCode")
                    success = True
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                    logger.warning(f"code 命令启动失败: {e}")

            if not success:
                logger.error("所有启动方式都失败了")
                return False

            # 等待应用启动（最多等待15秒）
            for i in range(15):
                time.sleep(1)
                if is_app_running(self.app_name) or self._check_vscode_process():
                    logger.info("VSCode 启动成功")
                    return True
                logger.debug(f"等待 VSCode 启动... ({i+1}/15)")

            logger.warning("VSCode 启动超时，但可能仍在后台启动")
            return True  # 返回 True，因为启动命令已执行

        except Exception as e:
            logger.error(f"启动 VSCode 时发生错误: {e}")
            return False

    @log_execution
    def quit_vscode(self) -> bool:
        """关闭 VSCode"""
        try:
            # 检查是否有 VSCode 进程运行
            if not is_app_running(self.app_name) and not self._check_vscode_process():
                logger.info("VSCode 未运行，无需关闭")
                return True

            logger.info("正在关闭 VSCode...")

            # 方式1: 使用 AppleScript 优雅地关闭 VSCode
            try:
                # 尝试多个可能的应用名称
                app_names = [self.app_name, "Visual Studio Code", "VSCode"]

                for app_name in app_names:
                    try:
                        script = f'''
                        tell application "{app_name}"
                            quit
                        end tell
                        '''
                        subprocess.run(['osascript', '-e', script], check=True, timeout=10)
                        logger.info(f"使用 AppleScript 关闭 VSCode (应用名: {app_name})")
                        break
                    except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                        continue

            except Exception as e:
                logger.warning(f"AppleScript 关闭失败: {e}")

            # 等待应用关闭（最多等待15秒）
            for i in range(15):
                if not is_app_running(self.app_name) and not self._check_vscode_process():
                    logger.info("VSCode 已成功关闭")
                    return True
                time.sleep(1)
                logger.debug(f"等待 VSCode 关闭... ({i+1}/15)")

            # 如果优雅关闭失败，尝试强制关闭
            logger.warning("优雅关闭超时，尝试强制关闭...")
            return self._force_quit_vscode()

        except Exception as e:
            logger.error(f"关闭 VSCode 时发生错误: {e}")
            # 尝试强制关闭
            return self._force_quit_vscode()

    def _check_vscode_process(self) -> bool:
        """检查 VSCode 进程是否运行"""
        try:
            # 检查常见的 VSCode 进程名
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            processes = result.stdout.lower()

            # 检查是否有 VSCode 相关进程
            for line in processes.split('\n'):
                if ('visual studio code' in line or 'vscode' in line or 
                    'code' in line and 'electron' in line):
                    return True

            return False
        except Exception as e:
            logger.debug(f"检查 VSCode 进程时出错: {e}")
            return False

    def _force_quit_vscode(self) -> bool:
        """强制关闭 VSCode"""
        try:
            import psutil
            killed_any = False

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    cmdline = ' '.join(proc_info['cmdline'] or []).lower()

                    # 检查是否是 VSCode 相关进程
                    if ('visual studio code' in proc_name or 'vscode' in proc_name or
                        'code' in proc_name or 'visual studio code' in cmdline or
                        'vscode' in cmdline):
                        logger.info(f"强制终止进程: {proc_info['name']} (PID: {proc_info['pid']})")
                        proc.terminate()
                        killed_any = True

                        # 等待进程终止
                        try:
                            proc.wait(timeout=10)
                        except psutil.TimeoutExpired:
                            # 如果进程仍未终止，强制杀死
                            proc.kill()
                            proc.wait(timeout=5)

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if killed_any:
                time.sleep(2)  # 等待进程完全关闭
                logger.info("VSCode 进程已强制关闭")
                return True
            else:
                logger.info("没有找到需要关闭的 VSCode 进程")
                return True

        except Exception as e:
            logger.error(f"强制关闭 VSCode 时发生错误: {e}")
            return False

    @log_execution
    def focus_vscode(self) -> bool:
        """将焦点切换到 VSCode"""
        try:
            # 使用 AppleScript 激活 VSCode
            script = f'''
            tell application "{self.app_name}"
                activate
            end tell
            '''
            subprocess.run(['osascript', '-e', script], check=True)
            time.sleep(1)  # 等待窗口激活
            logger.info("VSCode 窗口已激活")
            return True
        except Exception as e:
            logger.error(f"激活 VSCode 窗口失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def open_file(self, file_path: str) -> bool:
        """打开文件"""
        if not self.ensure_vscode_running():
            return False

        try:
            # 使用 Cmd+P 打开文件对话框
            self.focus_vscode()
            self.ui.press_hotkey('cmd', 'p')
            time.sleep(0.5)

            # 输入文件路径
            self.ui.type_text(file_path)
            time.sleep(0.5)

            # 按回车确认
            self.ui.press_key('return')

            logger.info(f"尝试打开文件: {file_path}")
            return True
        except Exception as e:
            logger.error(f"打开文件失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def run_current_file(self) -> bool:
        """运行当前文件"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 F5 运行当前文件
            self.ui.press_key('f5')
            logger.info("执行运行当前文件命令")
            return True
        except Exception as e:
            logger.error(f"运行文件失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def debug_current_file(self) -> bool:
        """调试当前文件"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 F5 启动调试（如果已配置）
            self.ui.press_key('f5')
            logger.info("执行调试当前文件命令")
            return True
        except Exception as e:
            logger.error(f"调试文件失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def find_in_files(self, search_text: str) -> bool:
        """在文件中查找"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 Cmd+Shift+F 打开全局搜索
            self.ui.press_hotkey('cmd', 'shift', 'f')
            time.sleep(0.5)

            # 输入搜索文本
            self.ui.type_text(search_text)
            time.sleep(0.5)

            # 按回车开始搜索
            self.ui.press_key('return')

            logger.info(f"在文件中搜索: {search_text}")
            return True
        except Exception as e:
            logger.error(f"文件搜索失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def goto_line(self, line_number: int) -> bool:
        """跳转到指定行"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 Ctrl+G 打开跳转到行对话框
            self.ui.press_hotkey('ctrl', 'g')
            time.sleep(0.5)

            # 输入行号
            self.ui.type_text(str(line_number))
            time.sleep(0.5)

            # 按回车跳转
            self.ui.press_key('return')

            logger.info(f"跳转到第 {line_number} 行")
            return True
        except Exception as e:
            logger.error(f"跳转到行失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def format_code(self) -> bool:
        """格式化代码"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 Shift+Alt+F 格式化代码
            self.ui.press_hotkey('shift', 'alt', 'f')
            logger.info("执行代码格式化")
            return True
        except Exception as e:
            logger.error(f"代码格式化失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def save_all(self) -> bool:
        """保存所有文件"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()
            # 使用 Cmd+K Cmd+S 保存所有文件
            self.ui.press_hotkey('cmd', 'k')
            time.sleep(0.2)
            self.ui.press_hotkey('cmd', 's')
            logger.info("保存所有文件")
            return True
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False

    @log_execution
    def clone_and_open_project(self, git_url: str, target_dir: str = None) -> bool:
        """克隆 Git 仓库并在 VSCode 中打开"""
        try:
            # 如果没有指定目标目录，使用默认目录
            if not target_dir:
                # 从 git URL 中提取项目名称
                project_name = git_url.split('/')[-1].replace('.git', '')
                target_dir = os.path.expanduser(f"~/Projects/{project_name}")

            # 确保目标目录的父目录存在
            parent_dir = os.path.dirname(target_dir)
            os.makedirs(parent_dir, exist_ok=True)

            # 如果目标目录已存在，先删除
            if os.path.exists(target_dir):
                logger.info(f"目标目录已存在，正在删除: {target_dir}")
                shutil.rmtree(target_dir)

            logger.info(f"正在克隆仓库: {git_url} -> {target_dir}")

            # 执行 git clone
            result = subprocess.run(
                ['git', 'clone', git_url, target_dir],
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )

            if result.returncode != 0:
                logger.error(f"Git clone 失败: {result.stderr}")
                return False

            logger.info("Git clone 成功完成")

            # 使用 VSCode 打开项目
            return self.open_project(target_dir)

        except subprocess.TimeoutExpired:
            logger.error("Git clone 超时")
            return False
        except Exception as e:
            logger.error(f"克隆项目失败: {e}")
            return False

    @log_execution
    def open_project(self, project_path: str) -> bool:
        """在 VSCode 中打开项目"""
        try:
            if not os.path.exists(project_path):
                logger.error(f"项目路径不存在: {project_path}")
                return False

            logger.info(f"正在用 VSCode 打开项目: {project_path}")

            # 使用 code 命令打开项目
            result = subprocess.run(
                ['code', project_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                logger.warning(f"code 命令失败，尝试其他方式: {result.stderr}")
                # 尝试先启动 VSCode，然后打开文件夹
                if self.launch_vscode():
                    time.sleep(2)
                    return self.open_folder_in_vscode(project_path)
                return False

            logger.info("项目已在 VSCode 中打开")
            return True

        except subprocess.TimeoutExpired:
            logger.error("打开项目超时")
            return False
        except Exception as e:
            logger.error(f"打开项目失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def open_folder_in_vscode(self, folder_path: str) -> bool:
        """在已运行的 VSCode 中打开文件夹"""
        try:
            self.focus_vscode()

            # 使用 Cmd+O 打开文件夹对话框
            self.ui.press_hotkey('cmd', 'o')
            time.sleep(1)

            # 使用 Cmd+Shift+G 打开路径输入框
            self.ui.press_hotkey('cmd', 'shift', 'g')
            time.sleep(0.5)

            # 输入文件夹路径
            self.ui.type_text(folder_path)
            time.sleep(0.5)

            # 按回车确认
            self.ui.press_key('return')
            time.sleep(0.5)

            # 点击打开按钮
            self.ui.press_key('return')

            logger.info(f"在 VSCode 中打开文件夹: {folder_path}")
            return True
        except Exception as e:
            logger.error(f"在 VSCode 中打开文件夹失败: {e}")
            return False

    @retry(max_attempts=3)
    @log_execution
    def open_augment_plugin(self, question: str = None) -> bool:
        """打开 Augment 插件并输入问题"""
        if not self.ensure_vscode_running():
            return False

        try:
            self.focus_vscode()

            # 使用 Cmd+Shift+P 打开命令面板
            self.ui.press_hotkey('cmd', 'shift', 'p')
            time.sleep(0.5)

            # 输入 Augment 命令
            self.ui.type_text("Augment")
            time.sleep(0.5)

            # 按回车选择第一个 Augment 命令
            self.ui.press_key('return')
            time.sleep(1)

            # 如果提供了问题，输入问题
            if question:
                logger.info(f"输入问题到 Augment: {question}")
                self.ui.type_text(question)
                time.sleep(0.5)
                # 按回车提交问题
                self.ui.press_key('return')

            logger.info("Augment 插件已打开")
            return True
        except Exception as e:
            logger.error(f"打开 Augment 插件失败: {e}")
            return False

    @log_execution
    def get_vscode_info(self) -> Dict[str, Any]:
        """获取 VSCode 信息"""
        return {
            'app_name': self.app_name,
            'bundle_id': self.bundle_id,
            'is_running': is_app_running(self.app_name),
            'screen_size': self.ui.get_screen_size(),
            'mouse_position': self.ui.get_mouse_position()
        }
