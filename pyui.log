2025-07-23 00:59:03,669 - src.utils - INFO - PyUI 应用启动
2025-07-23 00:59:03,669 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 00:59:03,669 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8080
2025-07-23 00:59:03,669 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 00:59:03,669 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 00:59:03,669 - src.utils - INFO - API 文档: http://127.0.0.1:8080/api/health
2025-07-23 00:59:38,693 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8080
2025-07-23 00:59:38,694 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:41:38,110 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:41:38] "[33mGET / HTTP/1.1[0m" 404 -
2025-07-23 09:41:38,874 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:41:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-23 09:43:59,512 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:43:59] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:46:38,975 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:46:38,976 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:46:38,976 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8080
2025-07-23 09:46:38,976 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:46:38,976 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:46:38,976 - src.utils - INFO - API 文档: http://127.0.0.1:8080/api/health
2025-07-23 09:46:47,301 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 09:46:47,302 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:46:47,302 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:46:47,302 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:46:47,302 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:46:52,426 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:46:52,426 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:46:52,426 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 09:46:52,426 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:46:52,427 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:46:52,427 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 09:46:52,459 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 09:46:52,459 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:47:12,679 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 09:47:12,681 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:47:12,683 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:47:12,683 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:47:12,683 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:47:26,418 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:47:26,418 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:47:26,419 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 09:47:26,419 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:47:26,419 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:47:26,419 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 09:47:26,462 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 09:47:26,462 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:47:33,042 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:47:33] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:47:38,243 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:47:38] "[32mHEAD / HTTP/1.1[0m" 302 -
2025-07-23 09:47:43,349 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:47:43] "[33mHEAD /static/index.html HTTP/1.1[0m" 404 -
2025-07-23 09:51:51,060 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 09:51:51,070 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:51:51,071 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:51:51,071 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:51:51,072 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:51:51,073 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:51:51,073 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:51:55,708 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:51:55,708 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:51:55,708 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 09:51:55,708 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:51:55,708 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:51:55,709 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 09:51:55,737 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 09:51:55,737 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:52:02,793 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:52:02] "[33mHEAD /static/index.html HTTP/1.1[0m" 404 -
2025-07-23 09:52:21,820 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 09:52:21,822 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:52:21,822 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:52:21,822 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:52:21,822 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:52:21,822 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:52:21,822 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:52:27,632 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:52:27,633 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:52:27,633 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 09:52:27,633 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:52:27,633 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:52:27,633 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 09:52:27,659 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 09:52:27,659 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:52:34,444 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:52:34] "[33mGET /static/index.html HTTP/1.1[0m" 404 -
2025-07-23 09:53:00,240 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 09:53:00,243 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:53:00,244 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:53:00,244 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:53:00,244 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 09:53:00,244 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 09:53:00,244 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 09:54:21,627 - src.utils - INFO - PyUI 应用启动
2025-07-23 09:54:21,628 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 09:54:21,628 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 09:54:21,630 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 09:54:21,630 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 09:54:21,630 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 09:54:21,733 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 09:54:21,734 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 09:54:28,915 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:28] "HEAD /static/index.html HTTP/1.1" 200 -
2025-07-23 09:54:34,168 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:34] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 09:54:34,170 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:34] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 09:54:39,908 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:39] "HEAD /static/style.css HTTP/1.1" 200 -
2025-07-23 09:54:44,398 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:44] "HEAD /static/script.js HTTP/1.1" 200 -
2025-07-23 09:54:50,389 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:54:50,501 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.11s)
2025-07-23 09:54:50,502 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:54:50] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:55:28,209 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:28] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 09:55:28,222 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:28] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 09:55:28,351 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:28] "GET /static/style.css HTTP/1.1" 200 -
2025-07-23 09:55:28,380 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:28] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 09:55:28,956 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:28] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:55:29,498 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:29] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-23 09:55:39,514 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:55:39,535 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:55:39,537 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:39] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:55:44,415 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 09:55:45,568 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_1.png
2025-07-23 09:55:45,569 - src.utils - INFO - 执行完成: take_screenshot (耗时: 1.15s)
2025-07-23 09:55:45,573 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:45] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 09:55:58,969 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:55:58] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:56:28,990 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:28] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:56:46,652 - src.utils - INFO - 开始执行: click
2025-07-23 09:56:46,955 - src.utils - INFO - 点击坐标 (100, 100), 按钮: left, 次数: 1
2025-07-23 09:56:46,955 - src.utils - INFO - 执行完成: click (耗时: 0.30s)
2025-07-23 09:56:46,956 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:46] "POST /api/mouse/click HTTP/1.1" 200 -
2025-07-23 09:56:54,063 - src.utils - INFO - 开始执行: click
2025-07-23 09:56:54,176 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:54] "[36mGET /static/index.html HTTP/1.1[0m" 304 -
2025-07-23 09:56:54,317 - src.utils - INFO - 点击坐标 (100, 100), 按钮: left, 次数: 1
2025-07-23 09:56:54,317 - src.utils - INFO - 执行完成: click (耗时: 0.25s)
2025-07-23 09:56:54,332 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:54] "POST /api/mouse/click HTTP/1.1" 200 -
2025-07-23 09:56:54,389 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:54] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-07-23 09:56:54,392 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:54] "[36mGET /static/script.js HTTP/1.1[0m" 304 -
2025-07-23 09:56:54,736 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:56:54] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:11,668 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:11] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:12,771 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:12] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:13,506 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:13] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:14,248 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:14] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:14,956 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:14] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:15,754 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:15,775 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.02s)
2025-07-23 09:57:15,775 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:15] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:16,474 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:16,488 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.01s)
2025-07-23 09:57:16,489 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:16] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:16,956 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:16,969 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.01s)
2025-07-23 09:57:16,969 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:16] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:17,690 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:17] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:19,333 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:19] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-23 09:57:19,341 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-07-23 09:57:20,442 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:20] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:24,760 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:24] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:26,584 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:29,901 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:29,909 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:29,911 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:29] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:31,376 - src.utils - INFO - 开始执行: focus_idea
2025-07-23 09:57:32,667 - src.utils - INFO - IDEA 窗口已激活
2025-07-23 09:57:32,682 - src.utils - INFO - 执行完成: focus_idea (耗时: 1.31s)
2025-07-23 09:57:32,685 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:32] "POST /api/idea/focus HTTP/1.1" 200 -
2025-07-23 09:57:35,599 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:35,601 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:35,605 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:35] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:36,914 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:36,915 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:36,915 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:36] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:37,824 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:37,827 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:37,833 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:37] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:39,526 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:39] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-23 09:57:40,382 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:40,384 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:40,386 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:40] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:42,583 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:42,612 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 09:57:42,615 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:42] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:43,801 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:43,849 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.05s)
2025-07-23 09:57:43,851 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:43] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:44,542 - src.utils - ERROR - API 错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-23 09:57:44,547 - src.utils - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 611, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/json/provider.py", line 187, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.5/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 43, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/src/http_server.py", line 226, in take_screenshot
    data = request.get_json() or {}
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 620, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/flask/wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/esign_code/py-ui/venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py", line 645, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)

2025-07-23 09:57:44,549 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:44] "[35m[1mPOST /api/screen/screenshot HTTP/1.1[0m" 500 -
2025-07-23 09:57:45,115 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:45,138 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.02s)
2025-07-23 09:57:45,139 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:45] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:45,579 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:45] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:57:46,932 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:46] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-23 09:57:47,771 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 09:57:47,801 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 09:57:47,802 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:47] "GET /api/info HTTP/1.1" 200 -
2025-07-23 09:57:54,737 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:57:54] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:58:24,744 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:58:24] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:58:54,738 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:58:54] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:59:24,739 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:59:24] "GET /api/health HTTP/1.1" 200 -
2025-07-23 09:59:54,745 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 09:59:54] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:07,348 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:00:07,350 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:00:07,350 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:00:07,351 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:00:07,351 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:00:07,351 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:00:07,351 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:00:13,969 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:00:13,969 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:00:13,969 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 10:00:13,970 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:00:13,970 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:00:13,970 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 10:00:14,020 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 10:00:14,020 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 10:00:24,784 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:24] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:25,157 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:25] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 10:00:25,460 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:25] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 10:00:25,744 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:25] "GET /static/style.css HTTP/1.1" 200 -
2025-07-23 10:00:25,745 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:25] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 10:00:26,365 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:30,143 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:30] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:30,907 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:30] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:33,352 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:00:33,427 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.07s)
2025-07-23 10:00:33,429 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:33] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:00:35,869 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:00:36,644 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_1.png
2025-07-23 10:00:36,645 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.77s)
2025-07-23 10:00:36,645 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:36] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:00:37,507 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:00:37,541 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 10:00:37,542 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:37] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:00:55,775 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:55] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:00:56,363 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:00:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:01:00,248 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:01:00] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:01:03,306 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:01:03,338 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 10:01:03,341 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:01:03] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:01:08,369 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:01:09,290 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_2.png
2025-07-23 10:01:09,291 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.92s)
2025-07-23 10:01:09,297 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:01:09] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:01:26,514 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:01:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:01:56,544 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:01:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:02:10,970 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:02:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:02:26,552 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:02:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:02:56,525 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:02:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:08,556 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:03:08,561 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:03:08,562 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:03:08,562 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:03:08,563 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:03:08,563 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:03:08,563 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:03:15,421 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:03:15,422 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:03:15,422 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 10:03:15,422 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:03:15,422 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:03:15,422 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 10:03:15,457 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 10:03:15,457 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 10:03:24,253 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:24] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 10:03:24,267 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:24] "[36mGET /static/index.html HTTP/1.1[0m" 304 -
2025-07-23 10:03:24,372 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-07-23 10:03:24,421 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:24] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 10:03:25,114 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:25] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:26,492 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:29,825 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:29] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:31,249 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:31] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:33,097 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:03:33,127 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 10:03:33,128 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:33] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:03:35,829 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:03:35,855 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.03s)
2025-07-23 10:03:35,857 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:35] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:03:55,561 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:55] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:03:56,487 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:03:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:04:10,061 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:04:10,118 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.06s)
2025-07-23 10:04:10,119 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:04:10] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:04:11,224 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:04:11] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:04:25,551 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:04:25] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:04:29,338 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:04:29,351 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:04:29,358 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:04:29,358 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:04:29,358 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:04:29,360 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:04:29,363 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:04:31,523 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:04:31,524 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:04:31,525 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:04:31,525 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:04:31,525 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:04:31,525 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:04:31,525 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:05:37,858 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:05:37,858 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:05:37,858 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 10:05:37,858 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:05:37,858 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:05:37,859 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 10:05:37,863 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-07-23 10:05:37,863 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 10:05:41,745 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:41] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:05:47,069 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:47] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:05:49,203 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:49] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:05:50,535 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:05:50,559 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.02s)
2025-07-23 10:05:50,559 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:50] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:05:54,444 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:05:54,470 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.02s)
2025-07-23 10:05:54,470 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:54] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:05:56,357 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:05:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:06:00,308 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:06:01,198 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_1.png
2025-07-23 10:06:01,199 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.89s)
2025-07-23 10:06:01,200 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:01] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:06:10,695 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:06:26,067 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:06:26,355 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:06:26,766 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_2.png
2025-07-23 10:06:26,767 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.70s)
2025-07-23 10:06:26,767 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:26] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:06:34,630 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:06:35,349 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_3.png
2025-07-23 10:06:35,350 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.72s)
2025-07-23 10:06:35,351 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:35] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:06:56,372 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:06:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:07:11,121 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:07:11] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:07:26,381 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:07:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:07:56,396 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:07:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:08:10,561 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:08:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:08:26,366 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:08:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:08:56,363 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:08:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:09:10,526 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:09:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:09:26,365 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:09:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:09:56,415 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:09:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:10:10,578 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:10:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:10:26,382 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:10:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:10:56,376 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:10:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:11:10,594 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:11:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:11:26,397 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:11:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:11:56,373 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:11:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:12:10,540 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:12:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:12:20,629 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:12:20,629 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:12:20,629 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8081
2025-07-23 10:12:20,629 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:12:20,629 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:12:20,630 - src.utils - INFO - API 文档: http://127.0.0.1:8081/api/health
2025-07-23 10:12:26,366 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:12:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:12:30,286 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:12:30,287 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:12:30,288 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:12:30,288 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:12:30,288 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:12:35,662 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:12:35,662 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:12:35,662 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8082
2025-07-23 10:12:35,662 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:12:35,662 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:12:35,662 - src.utils - INFO - API 文档: http://127.0.0.1:8082/api/health
2025-07-23 10:12:35,690 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8082
2025-07-23 10:12:35,690 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 10:12:45,849 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:12:45] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:12:51,711 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:12:51,731 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:12:56,428 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:12:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:13:10,630 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:13:23,090 - src.utils - ERROR - 应用 IntelliJ IDEA 启动超时
2025-07-23 10:13:23,093 - src.utils - ERROR - IDEA 启动失败
2025-07-23 10:13:23,094 - src.utils - INFO - 执行完成: launch_idea (耗时: 31.38s)
2025-07-23 10:13:23,104 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:23] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:13:26,373 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:13:41,556 - src.utils - INFO - 开始执行: quit_idea
2025-07-23 10:13:41,573 - src.utils - INFO - IDEA 未运行，无需关闭
2025-07-23 10:13:41,573 - src.utils - INFO - 执行完成: quit_idea (耗时: 0.02s)
2025-07-23 10:13:41,574 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:41] "POST /api/idea/quit HTTP/1.1" 200 -
2025-07-23 10:13:49,720 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:49] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 10:13:49,824 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:49] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 10:13:50,240 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:50] "GET /static/style.css HTTP/1.1" 200 -
2025-07-23 10:13:50,272 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:50] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 10:13:51,330 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:13:51,516 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:51] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-23 10:13:55,291 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:13:55,310 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:13:56,502 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:13:56] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:14:01,912 - src.utils - INFO - 开始执行: quit_idea
2025-07-23 10:14:01,930 - src.utils - INFO - IDEA 未运行，无需关闭
2025-07-23 10:14:01,930 - src.utils - INFO - 执行完成: quit_idea (耗时: 0.02s)
2025-07-23 10:14:01,931 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:01] "POST /api/idea/quit HTTP/1.1" 200 -
2025-07-23 10:14:10,590 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:14:21,246 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:14:26,222 - src.utils - ERROR - 应用 IntelliJ IDEA 启动超时
2025-07-23 10:14:26,225 - src.utils - ERROR - IDEA 启动失败
2025-07-23 10:14:26,225 - src.utils - INFO - 执行完成: launch_idea (耗时: 30.93s)
2025-07-23 10:14:26,227 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:26] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:14:26,535 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:26] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:14:51,914 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:14:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:15:06,413 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:15:06,456 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:15:10,652 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:15:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:15:10,768 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:15:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:15:21,256 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:15:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:15:38,112 - src.utils - ERROR - 应用 IntelliJ IDEA 启动超时
2025-07-23 10:15:38,114 - src.utils - ERROR - IDEA 启动失败
2025-07-23 10:15:38,116 - src.utils - INFO - 执行完成: launch_idea (耗时: 31.70s)
2025-07-23 10:15:38,123 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:15:38] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:15:51,255 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:15:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:16:10,649 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:16:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:16:10,702 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:16:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:16:21,256 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:16:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:16:51,250 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:16:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:17:02,585 - src.utils - INFO - 开始执行: focus_idea
2025-07-23 10:17:03,991 - src.utils - INFO - IDEA 窗口已激活
2025-07-23 10:17:03,997 - src.utils - INFO - 执行完成: focus_idea (耗时: 1.41s)
2025-07-23 10:17:04,005 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:04] "POST /api/idea/focus HTTP/1.1" 200 -
2025-07-23 10:17:08,099 - src.utils - INFO - 开始执行: quit_idea
2025-07-23 10:17:08,125 - src.utils - INFO - IDEA 未运行，无需关闭
2025-07-23 10:17:08,125 - src.utils - INFO - 执行完成: quit_idea (耗时: 0.03s)
2025-07-23 10:17:08,133 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:08] "POST /api/idea/quit HTTP/1.1" 200 -
2025-07-23 10:17:10,766 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:17:10,929 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:17:21,257 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:17:51,254 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:17:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:18:10,918 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:18:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:18:11,016 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:18:11] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:18:21,264 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:18:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:18:51,260 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:18:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:19:10,594 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:19:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:19:10,974 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:19:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:19:14,998 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:19:14,999 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:19:15,001 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:19:15,002 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:19:15,002 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:19:15,003 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:19:15,003 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:19:22,523 - src.utils - INFO - PyUI 应用启动
2025-07-23 10:19:22,523 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 10:19:22,523 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8082
2025-07-23 10:19:22,523 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 10:19:22,524 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 10:19:22,524 - src.utils - INFO - API 文档: http://127.0.0.1:8082/api/health
2025-07-23 10:19:22,558 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8082
2025-07-23 10:19:22,558 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 10:19:32,036 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:19:32,056 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:19:32,240 - src.utils - INFO - 使用 Bundle ID 启动 IDEA
2025-07-23 10:19:33,380 - src.utils - INFO - IDEA 启动成功
2025-07-23 10:19:33,380 - src.utils - INFO - 执行完成: launch_idea (耗时: 1.34s)
2025-07-23 10:19:33,382 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:19:33] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:19:45,986 - src.utils - INFO - 开始执行: quit_idea
2025-07-23 10:19:46,069 - src.utils - INFO - 正在关闭 IDEA...
2025-07-23 10:19:46,508 - src.utils - INFO - 使用 AppleScript 关闭 IDEA (应用名: IntelliJ IDEA 2024.3.2.2)
2025-07-23 10:19:51,827 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:19:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:20:03,455 - src.utils - WARNING - 优雅关闭超时，尝试强制关闭...
2025-07-23 10:20:03,483 - src.utils - INFO - 强制终止进程: node (PID: 29156)
2025-07-23 10:20:03,491 - src.utils - INFO - 强制终止进程: jcef Helper (GPU) (PID: 29177)
2025-07-23 10:20:03,502 - src.utils - INFO - 强制终止进程: jcef Helper (PID: 29192)
2025-07-23 10:20:03,558 - src.utils - INFO - 强制终止进程: jcef Helper (PID: 29193)
2025-07-23 10:20:03,658 - src.utils - INFO - 强制终止进程: jcef Helper (Renderer) (PID: 29195)
2025-07-23 10:20:03,694 - src.utils - INFO - 强制终止进程: java (PID: 33565)
2025-07-23 10:20:04,093 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 35025)
2025-07-23 10:20:04,125 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 35034)
2025-07-23 10:20:04,154 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 35436)
2025-07-23 10:20:04,170 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 35454)
2025-07-23 10:20:04,189 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 40094)
2025-07-23 10:20:04,220 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 40111)
2025-07-23 10:20:04,249 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 43840)
2025-07-23 10:20:04,264 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 43880)
2025-07-23 10:20:04,295 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 58008)
2025-07-23 10:20:04,325 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 58018)
2025-07-23 10:20:04,361 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 65065)
2025-07-23 10:20:04,377 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 65077)
2025-07-23 10:20:04,391 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67277)
2025-07-23 10:20:04,422 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67290)
2025-07-23 10:20:04,452 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67627)
2025-07-23 10:20:04,467 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67642)
2025-07-23 10:20:04,495 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67707)
2025-07-23 10:20:04,510 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 67724)
2025-07-23 10:20:04,540 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 74782)
2025-07-23 10:20:04,556 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 74803)
2025-07-23 10:20:04,585 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 79872)
2025-07-23 10:20:04,615 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 79911)
2025-07-23 10:20:04,647 - src.utils - INFO - 强制终止进程: java (PID: 83758)
2025-07-23 10:20:05,185 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 85231)
2025-07-23 10:20:05,215 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 85261)
2025-07-23 10:20:05,247 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 86292)
2025-07-23 10:20:05,280 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 86308)
2025-07-23 10:20:05,309 - src.utils - INFO - 强制终止进程: java (PID: 86578)
2025-07-23 10:20:05,717 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87061)
2025-07-23 10:20:05,734 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87067)
2025-07-23 10:20:05,766 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87308)
2025-07-23 10:20:05,781 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87310)
2025-07-23 10:20:05,797 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87400)
2025-07-23 10:20:05,813 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87416)
2025-07-23 10:20:05,828 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87859)
2025-07-23 10:20:05,844 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 87863)
2025-07-23 10:20:05,861 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 88187)
2025-07-23 10:20:05,876 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 88188)
2025-07-23 10:20:05,893 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 94336)
2025-07-23 10:20:05,925 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 94353)
2025-07-23 10:20:05,957 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 94482)
2025-07-23 10:20:05,989 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 94484)
2025-07-23 10:20:06,052 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 96042)
2025-07-23 10:20:06,068 - src.utils - INFO - 强制终止进程: language_server_macos_arm (PID: 96043)
2025-07-23 10:20:08,104 - src.utils - INFO - IDEA 进程已强制关闭
2025-07-23 10:20:08,104 - src.utils - INFO - 执行完成: quit_idea (耗时: 22.12s)
2025-07-23 10:20:08,105 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:20:08] "POST /api/idea/quit HTTP/1.1" 200 -
2025-07-23 10:20:10,553 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:20:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:20:10,556 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:20:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:20:21,343 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:20:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:20:51,272 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:20:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:21:10,551 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:21:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:21:10,556 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:21:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:21:21,261 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:21:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:21:51,270 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:21:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:10,504 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:10,539 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:21,326 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:51,249 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:54,051 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:54] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:22:57,997 - src.utils - INFO - 开始执行: get_idea_info
2025-07-23 10:22:58,019 - src.utils - INFO - 执行完成: get_idea_info (耗时: 0.02s)
2025-07-23 10:22:58,020 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:22:58] "GET /api/info HTTP/1.1" 200 -
2025-07-23 10:23:09,145 - src.utils - INFO - 开始执行: take_screenshot
2025-07-23 10:23:09,731 - src.utils - INFO - 截图已保存: ./screenshots/screenshot_1.png
2025-07-23 10:23:09,732 - src.utils - INFO - 执行完成: take_screenshot (耗时: 0.59s)
2025-07-23 10:23:09,733 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:09] "POST /api/screen/screenshot HTTP/1.1" 200 -
2025-07-23 10:23:10,529 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:23:10,535 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:23:14,319 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:23:14,333 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:23:14,413 - src.utils - INFO - 使用 Bundle ID 启动 IDEA
2025-07-23 10:23:15,559 - src.utils - INFO - IDEA 启动成功
2025-07-23 10:23:15,559 - src.utils - INFO - 执行完成: launch_idea (耗时: 1.24s)
2025-07-23 10:23:15,560 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:15] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:23:21,249 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:23:26,181 - src.utils - INFO - 开始执行: quit_idea
2025-07-23 10:23:26,303 - src.utils - INFO - 正在关闭 IDEA...
2025-07-23 10:23:26,776 - src.utils - INFO - 使用 AppleScript 关闭 IDEA (应用名: IntelliJ IDEA 2024.3.2.2)
2025-07-23 10:23:28,750 - src.utils - INFO - 开始执行: focus_idea
2025-07-23 10:23:30,025 - src.utils - INFO - IDEA 窗口已激活
2025-07-23 10:23:30,030 - src.utils - INFO - 执行完成: focus_idea (耗时: 1.28s)
2025-07-23 10:23:30,032 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:30] "POST /api/idea/focus HTTP/1.1" 200 -
2025-07-23 10:23:43,221 - src.utils - WARNING - 优雅关闭超时，尝试强制关闭...
2025-07-23 10:23:43,233 - src.utils - INFO - 强制终止进程: jcef Helper (GPU) (PID: 33688)
2025-07-23 10:23:43,242 - src.utils - INFO - 强制终止进程: jcef Helper (PID: 33689)
2025-07-23 10:23:43,302 - src.utils - INFO - 强制终止进程: jcef Helper (PID: 33690)
2025-07-23 10:23:45,375 - src.utils - INFO - IDEA 进程已强制关闭
2025-07-23 10:23:45,376 - src.utils - INFO - 执行完成: quit_idea (耗时: 19.19s)
2025-07-23 10:23:45,377 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:45] "POST /api/idea/quit HTTP/1.1" 200 -
2025-07-23 10:23:51,257 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:23:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:24:10,536 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:24:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:24:10,542 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:24:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:24:21,243 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:24:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:24:51,253 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:24:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:25:04,184 - src.utils - INFO - 开始执行: launch_idea
2025-07-23 10:25:04,201 - src.utils - INFO - 正在启动 IDEA...
2025-07-23 10:25:04,334 - src.utils - INFO - 使用 Bundle ID 启动 IDEA
2025-07-23 10:25:05,577 - src.utils - INFO - IDEA 启动成功
2025-07-23 10:25:05,578 - src.utils - INFO - 执行完成: launch_idea (耗时: 1.39s)
2025-07-23 10:25:05,580 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:05] "POST /api/idea/launch HTTP/1.1" 200 -
2025-07-23 10:25:10,547 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:25:10,562 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:25:21,258 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:25:32,362 - src.utils - INFO - 开始执行: type_text
2025-07-23 10:25:32,677 - src.utils - INFO - 输入文本: ai
2025-07-23 10:25:32,678 - src.utils - INFO - 执行完成: type_text (耗时: 0.32s)
2025-07-23 10:25:32,680 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:32] "POST /api/keyboard/type HTTP/1.1" 200 -
2025-07-23 10:25:35,908 - src.utils - INFO - 开始执行: type_text
2025-07-23 10:25:36,175 - src.utils - INFO - 输入文本: ai
2025-07-23 10:25:36,175 - src.utils - INFO - 执行完成: type_text (耗时: 0.27s)
2025-07-23 10:25:36,176 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:36] "POST /api/keyboard/type HTTP/1.1" 200 -
2025-07-23 10:25:51,247 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:51] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:25:53,479 - src.utils - INFO - 开始执行: click
2025-07-23 10:25:53,757 - src.utils - INFO - 点击坐标 (100, 100), 按钮: left, 次数: 1
2025-07-23 10:25:53,765 - src.utils - INFO - 执行完成: click (耗时: 0.29s)
2025-07-23 10:25:53,798 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:53] "POST /api/mouse/click HTTP/1.1" 200 -
2025-07-23 10:25:58,513 - src.utils - INFO - 开始执行: focus_idea
2025-07-23 10:25:59,882 - src.utils - INFO - IDEA 窗口已激活
2025-07-23 10:25:59,882 - src.utils - INFO - 执行完成: focus_idea (耗时: 1.37s)
2025-07-23 10:25:59,888 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:25:59] "POST /api/idea/focus HTTP/1.1" 200 -
2025-07-23 10:26:10,507 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:26:10,518 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:26:11,910 - src.utils - INFO - 开始执行: click
2025-07-23 10:26:12,136 - src.utils - INFO - 点击坐标 (100, 200), 按钮: left, 次数: 1
2025-07-23 10:26:12,170 - src.utils - INFO - 执行完成: click (耗时: 0.26s)
2025-07-23 10:26:12,290 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:12] "POST /api/mouse/click HTTP/1.1" 200 -
2025-07-23 10:26:21,250 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:21] "GET /api/health HTTP/1.1" 200 -
2025-07-23 10:26:23,815 - src.utils - INFO - 开始执行: type_text
2025-07-23 10:26:24,077 - src.utils - INFO - 输入文本: ai
2025-07-23 10:26:24,078 - src.utils - INFO - 执行完成: type_text (耗时: 0.26s)
2025-07-23 10:26:24,080 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:24] "POST /api/keyboard/type HTTP/1.1" 200 -
2025-07-23 10:26:24,819 - src.utils - INFO - 开始执行: type_text
2025-07-23 10:26:25,080 - src.utils - INFO - 输入文本: ai
2025-07-23 10:26:25,080 - src.utils - INFO - 执行完成: type_text (耗时: 0.26s)
2025-07-23 10:26:25,081 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 10:26:25] "POST /api/keyboard/type HTTP/1.1" 200 -
2025-07-23 10:26:35,571 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 10:26:35,572 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:26:35,572 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:26:35,572 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 10:26:35,573 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 10:26:35,573 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 10:26:35,573 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 17:53:19,974 - src.utils - INFO - PyUI 应用启动
2025-07-23 17:53:19,974 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 17:53:19,974 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8080
2025-07-23 17:53:19,974 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 17:53:19,975 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 17:53:19,975 - src.utils - INFO - API 文档: http://127.0.0.1:8080/api/health
2025-07-23 17:53:20,003 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8080
2025-07-23 17:53:20,003 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 17:53:36,295 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 17:53:36,300 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 17:53:36,302 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 17:53:36,302 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 17:53:36,302 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 17:53:36,302 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 17:53:36,302 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 17:54:47,114 - src.utils - INFO - PyUI 应用启动
2025-07-23 17:54:47,115 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 17:54:47,115 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8082
2025-07-23 17:54:47,115 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 17:54:47,115 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 17:54:47,115 - src.utils - INFO - API 文档: http://127.0.0.1:8082/api/health
2025-07-23 17:54:47,117 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8082
2025-07-23 17:54:47,118 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 17:55:06,442 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:06] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 17:55:06,499 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:06] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 17:55:06,568 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-07-23 17:55:06,572 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:06] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 17:55:07,223 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:07] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:55:10,690 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:55:37,233 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:55:37] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:56:07,235 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:56:07] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:56:10,649 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:56:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:56:37,229 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:56:37] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:57:07,236 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:57:07] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:57:10,683 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:57:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:57:37,237 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:57:37] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:58:07,236 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:58:07] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:58:10,691 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:58:10] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:58:37,232 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 17:58:37] "GET /api/health HTTP/1.1" 200 -
2025-07-23 17:59:06,688 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 17:59:06,690 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 17:59:06,691 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 17:59:06,691 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 17:59:06,691 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 17:59:06,691 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 17:59:06,691 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 17:59:50,707 - src.utils - INFO - PyUI 应用启动
2025-07-23 17:59:50,708 - src.utils - INFO - 正在启动 HTTP 服务器...
2025-07-23 17:59:50,708 - src.utils - INFO - 启动 HTTP 服务器: http://127.0.0.1:8084
2025-07-23 17:59:50,708 - src.utils - INFO - HTTP 服务器已启动
2025-07-23 17:59:50,708 - src.utils - INFO - 服务器模式运行中，按 Ctrl+C 退出
2025-07-23 17:59:50,708 - src.utils - INFO - API 文档: http://127.0.0.1:8084/api/health
2025-07-23 17:59:50,733 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8084
2025-07-23 17:59:50,733 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 18:00:42,937 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:42] "GET /api/health HTTP/1.1" 200 -
2025-07-23 18:00:43,610 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:43] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-23 18:00:44,463 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:44] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-23 18:00:44,478 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:44] "GET /static/index.html HTTP/1.1" 200 -
2025-07-23 18:00:44,512 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:44] "GET /static/style.css HTTP/1.1" 200 -
2025-07-23 18:00:44,521 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:44] "GET /static/script.js HTTP/1.1" 200 -
2025-07-23 18:00:44,966 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:00:44] "GET /api/health HTTP/1.1" 200 -
2025-07-23 18:01:06,500 - src.utils - INFO - 开始执行: move_mouse
2025-07-23 18:01:07,463 - src.utils - INFO - 鼠标移动到 (500, 300), 耗时: 0.5秒
2025-07-23 18:01:07,463 - src.utils - INFO - 执行完成: move_mouse (耗时: 0.96s)
2025-07-23 18:01:07,465 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:07] "POST /api/mouse/move HTTP/1.1" 200 -
2025-07-23 18:01:15,185 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:15] "GET /api/health HTTP/1.1" 200 -
2025-07-23 18:01:15,476 - src.utils - INFO - 开始执行: move_mouse
2025-07-23 18:01:16,322 - src.utils - INFO - 鼠标移动到 (500, 300), 耗时: 0.5秒
2025-07-23 18:01:16,322 - src.utils - INFO - 执行完成: move_mouse (耗时: 0.85s)
2025-07-23 18:01:16,323 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:16] "POST /api/mouse/move HTTP/1.1" 200 -
2025-07-23 18:01:18,437 - src.utils - INFO - 开始执行: move_mouse
2025-07-23 18:01:19,278 - src.utils - INFO - 鼠标移动到 (500, 300), 耗时: 0.5秒
2025-07-23 18:01:19,278 - src.utils - INFO - 执行完成: move_mouse (耗时: 0.84s)
2025-07-23 18:01:19,280 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:19] "POST /api/mouse/move HTTP/1.1" 200 -
2025-07-23 18:01:33,856 - src.utils - INFO - 开始执行: move_mouse
2025-07-23 18:01:34,708 - src.utils - INFO - 鼠标移动到 (500, 30), 耗时: 0.5秒
2025-07-23 18:01:34,709 - src.utils - INFO - 执行完成: move_mouse (耗时: 0.85s)
2025-07-23 18:01:34,710 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:34] "POST /api/mouse/move HTTP/1.1" 200 -
2025-07-23 18:01:36,809 - src.utils - INFO - 开始执行: move_mouse
2025-07-23 18:01:37,652 - src.utils - INFO - 鼠标移动到 (500, 30), 耗时: 0.5秒
2025-07-23 18:01:37,652 - src.utils - INFO - 执行完成: move_mouse (耗时: 0.84s)
2025-07-23 18:01:37,653 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:37] "POST /api/mouse/move HTTP/1.1" 200 -
2025-07-23 18:01:38,328 - src.utils - INFO - 开始执行: move_mouse_relative
2025-07-23 18:01:39,180 - src.utils - INFO - 鼠标相对移动 (50, 50), 耗时: 0.5秒
2025-07-23 18:01:39,181 - src.utils - INFO - 执行完成: move_mouse_relative (耗时: 0.85s)
2025-07-23 18:01:39,182 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:39] "POST /api/mouse/move_relative HTTP/1.1" 200 -
2025-07-23 18:01:44,991 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 18:01:44] "GET /api/health HTTP/1.1" 200 -
2025-07-23 18:02:07,145 - src.utils - INFO - 接收到信号 2，正在关闭应用...
2025-07-23 18:02:07,146 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 18:02:07,146 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 18:02:07,146 - src.utils - INFO - PyUI 应用已关闭
2025-07-23 18:02:07,146 - src.utils - INFO - 正在关闭 PyUI 应用...
2025-07-23 18:02:07,146 - src.utils - INFO - 等待服务器线程结束...
2025-07-23 18:02:07,146 - src.utils - INFO - PyUI 应用已关闭
