#!/usr/bin/env python3
"""
PyUI - macOS VSCode 自动化工具主程序
"""
import sys
import os
import argparse
import signal
import threading
import time
from typing import Optional

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import Config
from src.utils import logger
from src.http_server import run_server
from src.ui_automation import UIAutomation
from src.vscode_integration import VSCodeIntegration

class PyUIApplication:
    """PyUI 主应用类"""
    
    def __init__(self):
        self.server_thread: Optional[threading.Thread] = None
        self.running = False
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"接收到信号 {signum}，正在关闭应用...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_server(self):
        """启动 HTTP 服务器"""
        try:
            logger.info("正在启动 HTTP 服务器...")
            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            logger.info("HTTP 服务器已启动")
            return True
        except Exception as e:
            logger.error(f"启动 HTTP 服务器失败: {e}")
            return False
    
    def run_interactive_mode(self):
        """运行交互模式"""
        ui = UIAutomation()
        vscode = VSCodeIntegration()

        print("\n=== PyUI 交互模式 ===")
        print("可用命令:")
        print("  screenshot - 截取屏幕截图")
        print("  click <x> <y> - 点击指定坐标")
        print("  type <text> - 输入文本")
        print("  vscode-run - 运行当前 VSCode 文件")
        print("  vscode-debug - 调试当前 VSCode 文件")
        print("  vscode-open <file> - 打开文件")
        print("  clone <git_url> [target_dir] - 克隆 Git 仓库并打开")
        print("  augment [question] - 打开 Augment 插件")
        print("  info - 显示系统信息")
        print("  quit - 退出程序")
        print()
        
        while self.running:
            try:
                command = input("PyUI> ").strip()
                if not command:
                    continue
                
                parts = command.split()
                cmd = parts[0].lower()
                
                if cmd == 'quit':
                    break
                elif cmd == 'screenshot':
                    path = ui.take_screenshot()
                    print(f"截图已保存: {path}")
                elif cmd == 'click' and len(parts) >= 3:
                    x, y = int(parts[1]), int(parts[2])
                    success = ui.click(x, y)
                    print(f"点击 ({x}, {y}): {'成功' if success else '失败'}")
                elif cmd == 'type' and len(parts) >= 2:
                    text = ' '.join(parts[1:])
                    success = ui.type_text(text)
                    print(f"输入文本: {'成功' if success else '失败'}")
                elif cmd == 'vscode-run':
                    success = vscode.run_current_file()
                    print(f"运行文件: {'成功' if success else '失败'}")
                elif cmd == 'vscode-debug':
                    success = vscode.debug_current_file()
                    print(f"调试文件: {'成功' if success else '失败'}")
                elif cmd == 'vscode-open' and len(parts) >= 2:
                    file_path = ' '.join(parts[1:])
                    success = vscode.open_file(file_path)
                    print(f"打开文件: {'成功' if success else '失败'}")
                elif cmd == 'clone' and len(parts) >= 2:
                    git_url = parts[1]
                    target_dir = parts[2] if len(parts) >= 3 else None
                    success = vscode.clone_and_open_project(git_url, target_dir)
                    print(f"克隆项目: {'成功' if success else '失败'}")
                elif cmd == 'augment':
                    question = ' '.join(parts[1:]) if len(parts) >= 2 else None
                    success = vscode.open_augment_plugin(question)
                    print(f"打开 Augment: {'成功' if success else '失败'}")
                elif cmd == 'info':
                    info = vscode.get_vscode_info()
                    print(f"屏幕尺寸: {info['screen_size']}")
                    print(f"鼠标位置: {info['mouse_position']}")
                    print(f"VSCode 运行状态: {info['is_running']}")
                else:
                    print("未知命令或参数不足")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"执行命令时出错: {e}")
    
    def run(self, mode: str = 'server'):
        """运行应用"""
        logger.info("PyUI 应用启动")
        
        # 验证配置
        if not Config.validate():
            logger.error("配置验证失败")
            return False
        
        self.running = True
        self.setup_signal_handlers()
        
        try:
            if mode == 'server':
                # 服务器模式
                if not self.start_server():
                    return False
                
                logger.info("服务器模式运行中，按 Ctrl+C 退出")
                logger.info(f"API 文档: http://{Config.HOST}:{Config.PORT}/api/health")
                
                # 保持主线程运行
                while self.running:
                    time.sleep(1)
                    
            elif mode == 'interactive':
                # 交互模式
                self.run_interactive_mode()
                
            elif mode == 'both':
                # 同时运行服务器和交互模式
                if not self.start_server():
                    return False
                
                logger.info("混合模式运行中")
                self.run_interactive_mode()
                
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"应用运行时出错: {e}")
            return False
        finally:
            self.shutdown()
        
        return True
    
    def shutdown(self):
        """关闭应用"""
        logger.info("正在关闭 PyUI 应用...")
        self.running = False
        
        if self.server_thread and self.server_thread.is_alive():
            logger.info("等待服务器线程结束...")
            # 注意：Flask 开发服务器没有优雅关闭的方法
            # 在生产环境中应该使用 WSGI 服务器如 Gunicorn
        
        logger.info("PyUI 应用已关闭")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PyUI - macOS VSCode 自动化工具')
    parser.add_argument(
        '--mode', 
        choices=['server', 'interactive', 'both'],
        default='server',
        help='运行模式 (默认: server)'
    )
    parser.add_argument(
        '--host',
        default=Config.HOST,
        help=f'服务器主机地址 (默认: {Config.HOST})'
    )
    parser.add_argument(
        '--port',
        type=int,
        default=Config.PORT,
        help=f'服务器端口 (默认: {Config.PORT})'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    parser.add_argument(
        '--api-key',
        default=Config.API_KEY,
        help='API 密钥'
    )
    
    args = parser.parse_args()
    
    # 更新配置
    Config.HOST = args.host
    Config.PORT = args.port
    Config.DEBUG = args.debug
    Config.API_KEY = args.api_key
    
    # 显示启动信息
    print(f"PyUI v1.0.0 - macOS VSCode 自动化工具")
    print(f"模式: {args.mode}")
    print(f"服务器: http://{Config.HOST}:{Config.PORT}")
    print(f"API 密钥: {Config.API_KEY}")
    print(f"调试模式: {Config.DEBUG}")
    print()
    
    # 创建并运行应用
    app = PyUIApplication()
    success = app.run(args.mode)
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
